#ifndef __SysConfig_h
#define __SysConfig_h

/*MACRO*/
#define DEBUG            //调试模式必须保留 非调试可以注释

// 电机编码器配置 - MG513X霍尔编码器电机适配
#define MOTOR_FULL_VALUE 1560 //MG513X电机转一圈的编码值(13PPR*4倍频*30减速比)
#define MOTOR_ENCODER_PPR 13   //MG513X霍尔编码器分辨率(脉冲/转)
#define MOTOR_REDUCTION_RATIO 30 //MG513X电机减速比
#define MOTOR_ENCODER_4X_FREQ 4  //编码器4倍频系数

// MG513X电机特性参数
#define MG513X_RATED_VOLTAGE 12.0f  //额定电压12V
#define MG513X_RATED_CURRENT 0.8f   //额定电流约0.8A
#define MG513X_MAX_TORQUE 2.5f      //最大扭矩(估算值)

/*STD C*/
#include <stdint.h>
#include <stdarg.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <stdio.h>
#include <math.h>

/*TI CCS*/
#include "ti_msp_dl_config.h"
#include "ti/iqmath/include/IQmathLib.h"

/*BSP*/
#include "PID_IQMath.h"
#include "Task.h"
#include "Key_Led.h"
#include "SysTick.h"
#include "Motor.h"
#include "Serial.h"
#include "Tracker.h"
#include "OLED.h"
#include "MPU6050.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"
//#include "PID.h"

/*APP*/
#include "Task_App.h"
#include "Interrupt.h"

#endif
